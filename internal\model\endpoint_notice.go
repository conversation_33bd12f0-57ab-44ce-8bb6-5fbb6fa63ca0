package model

import (
	"time"
)

// EndpointNotice 终端通知模型
type EndpointNotice struct {
	ID        int       `json:"id" gorm:"primaryKey;autoIncrement;column:id"`
	Content   string    `json:"content" gorm:"type:text;comment:通知内容,是富文本内容;column:content"`
	Title     string    `json:"title" gorm:"type:varchar(255);not null;comment:告示标题;column:title"`
	Author    string    `json:"author" gorm:"type:varchar(255);not null;comment:告示撰写人;column:author"`
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at"`
}

// TableName 设置表名
func (EndpointNotice) TableName() string {
	return "endpoint_notice"
}
