package handler

import (
	"errors"
	myErrors "marketing/internal/pkg/errors"
	"marketing/internal/pkg/log"
	myValidator "marketing/internal/pkg/validator"
	"marketing/internal/pkg/wecom"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"
)

// RespBody response body.
// c.Set("responseBody", respBody) 仅仅只是为了存日志
type RespBody struct {
	// http code
	OK int `json:"ok"`
	// response message
	Message string `json:"msg"`
	// response data
	Data any `json:"data,omitempty"`
}

// Success 成功
func Success(c *gin.Context, data ...any) {
	respBody := &RespBody{
		OK:      1,
		Message: "ok",
	}
	if len(data) > 0 {
		respBody.Data = data[0]
	}
	c.Set("responseBody", respBody)
	c.JSON(http.StatusOK, respBody)
}

// Error 错误响应
func Error(c *gin.Context, err error) {
	var myErr *myErrors.Error
	var validationErrors validator.ValidationErrors
	var systemErr *myErrors.SystemError
	//前端只判断状态码 200就算成功 所以内部错误全部返回统一状态码500
	var statusCode = http.StatusInternalServerError
	respBody := &RespBody{
		OK: 0,
	}
	// 入参校验错误
	if errors.As(err, &validationErrors) {
		var sb strings.Builder
		for _, values := range myValidator.Translate(err) {
			for _, value := range values {
				sb.WriteString(value + ";\n ") // 写入值，并缩进
			}
		}
		respBody.Message = sb.String()
		// 记录验证错误的堆栈
		log.Error(err.Error(), zap.String("Stack", myErrors.LogStack(2, 12)))
		c.Set("responseBody", respBody)
		c.JSON(statusCode, respBody)
		return
	}
	// 自定义返回错误
	if errors.As(err, &myErr) {
		respBody.Message = err.Error()
		// 记录自定义错误的堆栈
		if myErr.Stack != "" {
			log.Error(err.Error(), zap.String("Stack", myErr.Stack))
		} else {
			log.Error(err.Error(), zap.String("Stack", myErrors.LogStack(2, 8)))
		}
		c.Set("responseBody", respBody)
		c.JSON(statusCode, respBody)
		return
	}
	//weChatAPIError
	var weChatErr *wecom.WeChatAPIError
	if errors.As(err, &weChatErr) {
		respBody.Message = weChatErr.ErrMsg
		// 记录微信API错误的堆栈
		log.Error(err.Error(), zap.String("Stack", myErrors.LogStack(2, 8)))
		c.Set("responseBody", respBody)
		c.JSON(statusCode, respBody)
		return
	}

	// 系统错误
	if errors.As(err, &systemErr) {
		log.Error(err.Error(), zap.String("Stack", systemErr.Stack))
		respBody.Message = "system error"
		c.Set("responseBody", respBody)
		c.JSON(statusCode, respBody)
		return
	}

	// unknown error
	log.Error(err.Error(), zap.String("Stack", myErrors.LogStack(2, 8)))
	respBody.Message = "unknown error"
	c.Set("responseBody", respBody)
	c.JSON(statusCode, respBody)
}

// ResponseWithCode 根据数据类型响应
func ResponseWithCode(c *gin.Context, code int, data any) {
	c.Set("responseBody", data) // 可以用于记录或其他目的

	switch v := data.(type) {
	case string:
		c.String(code, v)
	default:
		c.JSON(code, data)
	}
}
