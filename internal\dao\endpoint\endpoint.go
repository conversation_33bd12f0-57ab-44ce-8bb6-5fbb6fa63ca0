package endpoint

import (
	"errors"
	"fmt"
	"gorm.io/gorm/clause"
	api "marketing/internal/api/endpoint"
	warrantyApi "marketing/internal/api/warranty"
	"marketing/internal/consts"
	"marketing/internal/model"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/utils"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type EndpointDao interface {
	GetAllEndpoints(c *gin.Context, filter api.GetEndpointReq) ([]*model.Endpoint, int64, error)
	GetEndpointByID(c *gin.Context, id uint) (*model.Endpoint, error)
	CreateEndpoint(c *gin.Context, endpoint *model.Endpoint) (*model.Endpoint, error)
	UpdateEndpoint(c *gin.Context, endpoint *model.Endpoint) (*model.Endpoint, error)
	GetAgencies(c *gin.Context, ids []int) (map[uint]string, error)
	GetImageUpdatedAtMap(c *gin.Context, endpointIds []uint) (map[uint]time.Time, error)
	GetInfoUpdatedMap(c *gin.Context, endpointIds []uint) (map[uint]time.Time, error)
	GetEndpointByAgency(c *gin.Context, agencyID uint, status int) (*[]model.Endpoint, error)
	GetAllEndpoint(c *gin.Context) ([]map[string]any, error)
	GetTree(c *gin.Context, endpoint bool) ([]*DepartmentNode, error)
	GetEndpointMap(c *gin.Context, ids []int) (map[uint]string, error)

	GetEndpointByName(c *gin.Context, name string) []*warrantyApi.GetEndpointResp
	GetUserEndpointInfo(c *gin.Context, id int) (*api.EndpointInfoWithC, error)
	ActivateEndpoint(c *gin.Context, endpointID int) error
	CheckEndpointPermission(c *gin.Context, endpoint int, permission string) (bool, error)
	GrantH5PermissionToEndpoint(c *gin.Context, endpoint *model.Endpoint) error
}

type endpoint struct {
	db *gorm.DB
}

func NewEndpointDao(db *gorm.DB) EndpointDao {
	return &endpoint{
		db: db,
	}
}

type DepartmentNode struct {
	ID        uint              `json:"id"`
	ParentID  uint              `json:"parent_id"`
	Name      string            `json:"name"`
	Rank      int               `json:"rank" gorm:"-"`
	Level     int               `json:"level"`
	LevelName string            `json:"level_name" gorm:"-"`
	Children  []*DepartmentNode `json:"children,omitempty" gorm:"-"`
}

func (e *DepartmentNode) GetID() uint       { return e.ID }
func (e *DepartmentNode) GetParentID() uint { return e.ParentID }
func (e *DepartmentNode) GetRank() int      { return e.Rank }
func (e *DepartmentNode) AddChild(child utils.TreeNode) {
	e.Children = append(e.Children, child.(*DepartmentNode))
}
func (e *DepartmentNode) IsLeaf() bool {
	return e.Level == 3
}

// GetAllEndpoints 获取所有 Endpoint 数据
func (e *endpoint) GetAllEndpoints(c *gin.Context, filter api.GetEndpointReq) ([]*model.Endpoint, int64, error) {
	var endpoints []*model.Endpoint
	var totalCount int64

	// 基本查询
	query := e.db.Model(&model.Endpoint{})

	// 根据过滤器条件添加查询条件
	if filter.Name != "" {
		query = query.Where("name LIKE ?", "%"+filter.Name+"%")
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.Code != "" {
		query = query.Where("code = ?", filter.Code)
	}

	if filter.IsPreSale != "" {
		query = query.Where("is_pre_sale = ?", filter.IsPreSale)
	}
	if filter.IsAfterSale != "" {
		query = query.Where("is_after_sale = ?", filter.IsAfterSale)
	}
	if filter.OpenStatus != "" {
		query = query.Where("open_status = ?", filter.OpenStatus)
	}

	if filter.Phone != "" {
		query = query.Where("phone LIKE ?", "%"+filter.Phone+"%")
	}
	if filter.TopAgency != 0 {
		query = query.Where("top_agency = ?", filter.TopAgency)
	}
	if filter.SecondAgency != 0 {
		query = query.Where("second_agency = ?", filter.SecondAgency)
	}

	// 获取总记录数
	err := query.Count(&totalCount).Error
	query = query.Order("id desc")

	// 查询结果进行分页
	err = query.Limit(filter.PageSize).Offset((filter.Page - 1) * filter.PageSize).Find(&endpoints).Error
	if err != nil { // 如果出现错误，返回错误信息

		return nil, 0, err
	}

	return endpoints, totalCount, nil // 返回查询的结果和分页信息

}

// GetEndpointByID 根据 ID 查询指定 Endpoint 数据
func (e *endpoint) GetEndpointByID(c *gin.Context, id uint) (*model.Endpoint, error) {
	var info model.Endpoint
	if err := e.db.WithContext(c).Where("id = ?", id).First(&info).Error; err != nil {
		return nil, err
	}
	return &info, nil
}

// CreateEndpoint 插入新的 Endpoint 数据
func (e *endpoint) CreateEndpoint(c *gin.Context, endpoint *model.Endpoint) (*model.Endpoint, error) {
	// 生成 code
	code, err := e.generateCode(consts.EndpointType(endpoint.Type))
	if err != nil {
		return nil, err
	}
	endpoint.Code = code
	err = e.db.WithContext(c).Create(&endpoint).Error
	return endpoint, err
}

// UpdateEndpoint 更新指定 ID 的 Endpoint 数据
func (e *endpoint) UpdateEndpoint(c *gin.Context, endpoint *model.Endpoint) (*model.Endpoint, error) {
	if err := e.db.WithContext(c).Save(endpoint).Error; err != nil {
		return nil, err
	}
	return endpoint, nil
}

// 生成code 生成code
func (e *endpoint) generateCode(endpointType consts.EndpointType) (string, error) {

	var maxCode int
	result := e.db.Model(&model.Endpoint{}).
		Select("MAX(CAST(RIGHT(code, 5) AS UNSIGNED)) as code").
		Where("type = ?", endpointType).
		Scan(&maxCode)

	if result.Error != nil {
		return "", result.Error
	}

	for {
		maxCode++
		// 检查是否包含数字 4
		if strings.Contains(fmt.Sprintf("%05d", maxCode), "4") {
			continue // 如果包含数字 4，则继续递增
		}

		// 找到符合条件的编码，输出并结束循环
		EndpointTypeToPrefixMap := consts.GetEndpointTypePrefix()
		return fmt.Sprintf("%s%05d", EndpointTypeToPrefixMap[endpointType], maxCode), nil
	}
}

func (e *endpoint) GetAgencies(c *gin.Context, ids []int) (map[uint]string, error) {
	type agency struct {
		ID   uint
		Name string
	}
	var agencies = make(map[uint]string)
	var data []agency
	err := e.db.WithContext(c).Table("agency").Where("id IN ?", ids).Select("id", "name").Scan(&data).Error
	if err != nil {
		return agencies, err
	}
	for _, v := range data {
		agencies[v.ID] = v.Name
	}
	return agencies, err
}

func (e *endpoint) GetImageUpdatedAtMap(c *gin.Context, endpointIds []uint) (map[uint]time.Time, error) {
	type Result struct {
		EndpointID uint      `gorm:"column:endpoint_id"`
		UpdatedAt  time.Time `gorm:"column:updated_at"`
	}

	var results []Result
	err := e.db.WithContext(c).Table("endpoint_image_apply").
		Where("endpoint_id IN ?", endpointIds).
		Select("endpoint_id, MAX(updated_at) as updated_at").
		Group("endpoint_id").
		Scan(&results).Error
	if err != nil {
		return nil, err
	}

	imageUpdatedAtMap := make(map[uint]time.Time)
	for _, result := range results {
		imageUpdatedAtMap[result.EndpointID] = result.UpdatedAt
	}

	return imageUpdatedAtMap, nil
}

func (e *endpoint) GetInfoUpdatedMap(c *gin.Context, endpointIds []uint) (map[uint]time.Time, error) {
	type Result struct {
		EndpointID uint      `gorm:"column:endpoint_id"`
		UpdatedAt  time.Time `gorm:"column:updated_at"`
	}

	var results []Result
	err := e.db.WithContext(c).Table("endpoint_info_apply").
		Where("endpoint_id IN ?", endpointIds).
		Select("endpoint_id, MAX(updated_at) as updated_at").
		Group("endpoint_id").
		Scan(&results).Error
	if err != nil {
		return nil, err
	}

	infoUpdatedMap := make(map[uint]time.Time)
	for _, result := range results {
		infoUpdatedMap[result.EndpointID] = result.UpdatedAt
	}

	return infoUpdatedMap, nil
}

func (e *endpoint) GetEndpointByAgency(c *gin.Context, agencyID uint, status int) (*[]model.Endpoint, error) {
	var endpoints []model.Endpoint
	query := e.db.WithContext(c).Where("top_agency = ? or second_agency = ?", agencyID, agencyID)
	if status != 0 {
		query = query.Where("status = ?", status)
	}
	err := query.Find(&endpoints).Error
	return &endpoints, err
}

func (e *endpoint) GetAllEndpoint(c *gin.Context) ([]map[string]any, error) {

	type Agency struct {
		ID       uint   `json:"id"`
		Name     string `json:"name"`
		Level    int    `json:"level"`
		ParentId uint   `json:"parentId"`
	}

	var agencies []Agency
	err := e.db.WithContext(c).Table("agency").Select("id, name,level,pid as parent_id").
		Where("deleted_at is null").Find(&agencies).Error
	if err != nil {
		return nil, err
	}
	var endpoints []Agency
	err = e.db.WithContext(c).Table("endpoint").Select([]string{
		"id", "name", "3 as level",
		"IF(second_agency = 0, top_agency, second_agency) AS parent_id",
	}).Where("status = 1").Find(&endpoints).Error
	if err != nil {
		return nil, err
	}

	// Convert the slice of structs to a slice of maps
	var result []map[string]any
	for _, agency := range agencies {
		result = append(result, map[string]any{
			"id":       strconv.Itoa(int(agency.ID)),
			"name":     agency.Name,
			"parentId": agency.ParentId,
			"level":    agency.Level,
		})
	}
	for _, value := range endpoints {
		result = append(result, map[string]any{
			"id":       strconv.Itoa(int(value.ID)),
			"name":     value.Name,
			"parentId": value.ParentId,
			"level":    value.Level,
		})
	}

	return result, err
}

func (e *endpoint) GetTree(c *gin.Context, endpoint bool) ([]*DepartmentNode, error) {
	var agencies []DepartmentNode
	var result []*DepartmentNode
	err := e.db.WithContext(c).Table("agency").Select("id, name,level,pid as parent_id").
		Where("deleted_at is null").Find(&agencies).Error
	if err != nil {
		return nil, err
	}
	for i := range agencies {
		switch agencies[i].Level {
		case 1:
			agencies[i].LevelName = "一级"
		case 2:
			agencies[i].LevelName = "二级"
		}
		result = append(result, &agencies[i])
	}
	tree := utils.BuildTree(result)

	if endpoint {
		var endpoints []DepartmentNode
		err = e.db.WithContext(c).Table("endpoint").Select([]string{
			"id", "name", "3 as level",
			"IF(second_agency = 0, top_agency, second_agency) AS parent_id",
		}).Where("status = 1").Find(&endpoints).Error
		if err != nil {
			return nil, err
		}
		for i := range endpoints {
			endpoints[i].LevelName = "终端"
		}

		// 递归处理树形结构
		var processNode func(node *DepartmentNode)
		processNode = func(node *DepartmentNode) {
			// 如果是终端节点，不继续处理
			if node.Level == 3 {
				return
			}
			// 检查当前节点是否有对应的终端节点
			for i := range endpoints {
				if node.ID == endpoints[i].GetParentID() {
					node.AddChild(&endpoints[i])
				}
			}
			// 递归处理子节点
			for _, child := range node.Children {
				processNode(child)
			}
		}

		// 处理每个根节点
		for i := range tree {
			processNode(tree[i])
		}
	}

	return tree, err
}

func (e *endpoint) GetEndpointMap(c *gin.Context, ids []int) (map[uint]string, error) {
	type endpoint struct {
		ID   uint
		Name string
	}
	var endpoints = make(map[uint]string)
	var data []endpoint
	err := e.db.WithContext(c).Table("endpoint").Where("id IN ?", ids).Select("id", "name").Scan(&data).Error
	if err != nil {
		return endpoints, err
	}
	for _, v := range data {
		endpoints[v.ID] = v.Name
	}
	return endpoints, err
}

// GetEndpointByName 提供给Warranty模块使用
func (e *endpoint) GetEndpointByName(c *gin.Context, name string) []*warrantyApi.GetEndpointResp {
	var resp []*warrantyApi.GetEndpointResp

	if matched, _ := regexp.MatchString(`^\w+$`, name); matched {
		// 按照账号搜索
		e.db.WithContext(c).Table("endpoint AS e").
			Select("e.id, CONCAT(e.name, '【', e.address, '】-', u.username) AS text").
			Joins("LEFT JOIN user_endpoint AS ue ON ue.endpoint = e.id").
			Joins("LEFT JOIN admin_users AS u ON ue.uid = u.id").
			Where("e.status = ?", 1).
			Where("u.username LIKE ?", name+"%").
			Limit(10).
			Scan(&resp)
	} else {
		// 按照终端名称搜索
		e.db.WithContext(c).Table("endpoint").
			Select("id, CONCAT(name, '【', address, '】') AS text").
			Where("status = ?", 1).
			Where("name LIKE ?", "%"+name+"%").
			Limit(10).
			Scan(&resp)
	}
	return resp
}

func (e *endpoint) GetUserEndpointInfo(c *gin.Context, uid int) (*api.EndpointInfoWithC, error) {
	var ep api.EndpointInfoWithC
	err := e.db.WithContext(c).Table("user_endpoint as uep").
		Select("ep.id, ep.name, ep.address, ep.phone, ep.manager, ep.top_agency, ep.second_agency, a.channel").
		Joins("LEFT JOIN endpoint ep ON uep.endpoint = ep.id").
		Joins("LEFT JOIN agency a ON IF(ep.second_agency = 0, ep.top_agency, ep.second_agency) = a.id").
		Where("uep.uid = ? AND ep.status = 1", uid).
		Scan(&ep).Error
	if err != nil {
		return nil, err
	}
	return &ep, nil
}

func (e *endpoint) ActivateEndpoint(c *gin.Context, endpointID int) error {
	res := e.db.WithContext(c).
		Model(&model.Endpoint{}).
		Where("id = ?", endpointID).
		Update("active_at", time.Now())
	if res.Error != nil {
		log.New().Error(fmt.Sprintf("activateEndpoint %d failed: %v", endpointID, res.Error))
		return res.Error
	}

	return nil
}

func (e *endpoint) CheckEndpointPermission(c *gin.Context, endpoint int, permission string) (bool, error) {
	var count int64
	err := e.db.WithContext(c).
		Table("admin_permissions as ap").
		Joins("RIGHT JOIN endpoint_permissions ep ON ap.id = ep.permission_id").
		Where("ep.endpoint_id = ? AND ap.slug = ?", endpoint, permission).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// GrantH5PermissionToEndpoint 为新终端授予H5权限
func (e *endpoint) GrantH5PermissionToEndpoint(c *gin.Context, endpoint *model.Endpoint) error {
	// 1. 检查符合条件的经销商数量
	var count int64
	agencyIDs := []interface{}{endpoint.TopAgency, endpoint.SecondAgency}

	if err := e.db.WithContext(c).Table("fx_dealer").
		Where("agency_id IN ?", agencyIDs).
		Where("attainable = ?", 1).
		Where("deleted_at IS NULL").
		Count(&count).Error; err != nil {
		return err
	}

	if count == 0 {
		return nil
	}

	// 2. 获取权限ID
	var permissionID uint
	if err := e.db.WithContext(c).Table("admin_permissions").Where("slug = ?", "h5-zhuoyi").
		Select("id").
		Scan(&permissionID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil // 权限不存在
		}
		return err
	}
	if permissionID == 0 {
		return nil // 权限不存在
	}

	// 3. 在事务中执行插入
	return e.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		// 使用 INSERT IGNORE 防止重复
		if err := tx.Clauses(clause.Insert{Modifier: "IGNORE"}).
			Create(&model.EndpointPermission{
				EndpointID:   uint(endpoint.ID),
				PermissionID: permissionID,
			}).Error; err != nil {
			return err
		}

		return nil
	})
}
