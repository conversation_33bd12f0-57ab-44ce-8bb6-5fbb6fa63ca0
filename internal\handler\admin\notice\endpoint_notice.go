package notice

import (
	"marketing/internal/handler"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

type EndpointNoticeHandler interface {
	Lists(c *gin.Context)
	Get(c *gin.Context)
	Create(c *gin.Context)
	Update(c *gin.Context)
	Delete(c *gin.Context)
}

type endpointNoticeHandler struct {
	svc service.EndpointNoticeSvc
}

func NewEndpointNoticeHandler(svc service.EndpointNoticeSvc) EndpointNoticeHandler {
	return &endpointNoticeHandler{
		svc: svc,
	}
}

// Lists 获取终端通知列表
func (h *endpointNoticeHandler) Lists(c *gin.Context) {
	title := e.ReqParamStr(c, "title")
	author := e.ReqParamStr(c, "author")
	pageNum := e.ReqParamInt(c, "page_num", 1)
	pageSize := e.ReqParamInt(c, "page_size", 20)

	list, total, err := h.svc.GetEndpointNoticeList(c, title, author, pageNum, pageSize)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{
		"data":      list,
		"total":     total,
		"page":      pageNum,
		"page_size": pageSize,
	})
}

// Get 获取指定终端通知
func (h *endpointNoticeHandler) Get(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		handler.Error(c, errors.NewErr("无效的ID"))
		return
	}

	notice, err := h.svc.GetEndpointNoticeById(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}

	if notice == nil {
		handler.Error(c, errors.NewErr("通知不存在"))
		return
	}

	handler.Success(c, gin.H{
		"data": notice,
	})
}

// Create 创建终端通知
func (h *endpointNoticeHandler) Create(c *gin.Context) {
	var req CreateEndpointNoticeReq
	if err := c.ShouldBindJSON(&req); err != nil {
		handler.Error(c, err)
		return
	}

	id, err := h.svc.CreateEndpointNotice(c, req.Title, req.Author, req.Content)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{
		"id": id,
	})
}

// Update 更新终端通知
func (h *endpointNoticeHandler) Update(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		handler.Error(c, errors.NewErr("无效的ID"))
		return
	}

	var req UpdateEndpointNoticeReq
	if err := c.ShouldBindJSON(&req); err != nil {
		handler.Error(c, err)
		return
	}

	err = h.svc.UpdateEndpointNotice(c, id, req.Title, req.Author, req.Content)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, nil)
}

// Delete 删除终端通知
func (h *endpointNoticeHandler) Delete(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		handler.Error(c, errors.NewErr("无效的ID"))
		return
	}

	err = h.svc.DeleteEndpointNotice(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, nil)
}

// CreateEndpointNoticeReq 创建终端通知请求
type CreateEndpointNoticeReq struct {
	Title   string `json:"title" binding:"required"`
	Author  string `json:"author" binding:"required"`
	Content string `json:"content" binding:"required"`
}

// UpdateEndpointNoticeReq 更新终端通知请求
type UpdateEndpointNoticeReq struct {
	Title   string `json:"title" binding:"required"`
	Author  string `json:"author" binding:"required"`
	Content string `json:"content" binding:"required"`
}
