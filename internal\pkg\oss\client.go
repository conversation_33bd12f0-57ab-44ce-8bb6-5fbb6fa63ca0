package oss

import (
	"context"
	"fmt"
	"io"
	"log"
	"sync"

	"github.com/aliyun/alibabacloud-oss-go-sdk-v2/oss"
	"github.com/aliyun/alibabacloud-oss-go-sdk-v2/oss/credentials"
	"github.com/spf13/viper"
)

var (
	client *oss.Client
	once   sync.Once
)

func NewClient() *oss.Client {
	once.Do(func() {
		region := viper.GetString("oss.region")
		provider := credentials.NewEnvironmentVariableCredentialsProvider()

		cfg := oss.LoadDefaultConfig().
			WithCredentialsProvider(provider).
			WithRegion(region)

		client = oss.NewClient(cfg)
	})
	return client
}

func Download(objectKey string) ([]byte, error) {
	c := NewClient()
	ctx := context.Background()

	// 获取对象
	resp, err := c.GetObject(ctx, &oss.GetObjectRequest{
		Bucket: oss.Ptr(viper.GetString("oss.bucket")),
		Key:    oss.Ptr(objectKey),
	})
	if err != nil {
		return nil, fmt.Errorf("获取对象失败: %w", err)
	}
	defer resp.Body.Close()

	// 一次性读取整个文件内容
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Fatalf("failed to read object %v", err)
	}

	return data, nil
}
