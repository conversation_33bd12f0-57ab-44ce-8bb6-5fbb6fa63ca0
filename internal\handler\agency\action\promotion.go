package action

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"marketing/internal/api/action"
	"marketing/internal/consts"
	"marketing/internal/handler"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/service"
	"marketing/internal/service/system"
	"sort"
	"strconv"
)

type PromotionHandle struct {
	svc         service.PromotionService
	userService system.AdminUserInterface
}

func NewPromotionHandle(svc service.PromotionService, userService system.AdminUserInterface) *PromotionHandle {
	return &PromotionHandle{svc: svc, userService: userService}
}

func (h *PromotionHandle) GetPromotion(c *gin.Context) {
	var req action.PromotionsReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	promoType := cast.ToUint(c.Param("type"))
	if promoType == 0 {
		handler.Error(c, appError.NewErr("type不能为空"))
		return
	}
	req.Type = promoType
	req.SetDefaults()
	//获取用户代理
	agency, err := h.userService.GetUserAgency(c, c.GetUint("uid"))
	if err != nil || agency == nil || agency.ID == 0 {
		handler.Error(c, appError.NewErr("用户代理不存在"))
		return
	}
	req.AgencyID = agency.ID
	data, total, err := h.svc.GetList(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"total": total,
		"data":  data,
	})
}

// GetInfo 获取活动详情
func (h *PromotionHandle) GetInfo(c *gin.Context) {
	id := c.Param("id")
	info, err := h.svc.GetInfo(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, info)
}

// GetJoinList 获取参与名单列表
func (h *PromotionHandle) GetJoinList(c *gin.Context) {
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, err := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	endpoint := c.Query("endpoint_id")
	model := c.Query("model")
	status := c.Query("status")
	id, err := strconv.Atoi(c.Query("promotion_id"))
	if err != nil {
		handler.Error(c, err)
		return
	}
	secondAgencyId := c.GetUint("second_agency_id")
	barcode := c.Query("barcode")
	//获取用户代理
	agency, err := h.userService.GetUserAgency(c, c.GetUint("uid"))
	if err != nil || agency == nil || agency.ID == 0 {
		handler.Error(c, appError.NewErr("用户代理不存在"))
		return
	}
	agencyID := cast.ToString(agency.ID)
	if secondAgencyId > 0 && agency.Level == 1 {
		agencyID = cast.ToString(secondAgencyId)
	}
	list, total, err := h.svc.GetJoinList(c, id, page, pageSize, agencyID, endpoint, status, model, barcode)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list":      list,
		"page":      page,
		"page_size": pageSize,
		"total":     total,
	})
}

func (h *PromotionHandle) Receipt(c *gin.Context) {
	param := c.Param("id")
	id, err := strconv.Atoi(param)
	if err != nil {
		handler.Error(c, err)
		return
	}
	type Req struct {
		Receipt []string `json:"receipt"`
	}
	var req Req
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	uid := c.GetUint("uid")
	agency, err := h.userService.GetUserAgency(c, uid)
	if err != nil || agency == nil || agency.ID == 0 {
		handler.Error(c, appError.NewErr("用户代理不存在"))
		return
	}
	err = h.svc.AgencyReceipt(c, id, agency.ID, req.Receipt)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (h *PromotionHandle) GetPromotionType(c *gin.Context) {
	data := consts.GetPromotionTypeMap()
	var res []map[string]interface{}
	for k, v := range data {
		res = append(res, map[string]interface{}{
			"value": k,
			"label": v,
		})
	}
	// 按 value 排序
	sort.Slice(res, func(i, j int) bool {
		return res[i]["value"].(uint8) < res[j]["value"].(uint8)
	})
	handler.Success(c, res)
}

// GetJoinInfo 获取参与详情
func (h *PromotionHandle) GetJoinInfo(c *gin.Context) {
	id := c.Param("id")
	store, order, err := h.svc.GetJoinInfo(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"store": store,
		"order": order,
	})
}

func (h *PromotionHandle) Export(c *gin.Context) {
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, err := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	agency := c.Query("agency_id")
	endpoint := c.Query("endpoint_id")
	model := c.Query("model_id")
	status := c.Query("status")
	id, err := strconv.Atoi(c.Query("promotion_id"))
	if err != nil {
		handler.Error(c, err)
		return
	}
	//获取用户代理
	agencyData, err := h.userService.GetUserAgency(c, c.GetUint("uid"))
	if err != nil || agencyData == nil || agencyData.ID == 0 {
		handler.Error(c, appError.NewErr("用户代理不存在"))
		return
	}
	agency = cast.ToString(agencyData.ID)
	err = h.svc.Export(c, id, page, pageSize, agency, endpoint, model, status)
	if err != nil {
		handler.Error(c, err)
		return
	}
}
