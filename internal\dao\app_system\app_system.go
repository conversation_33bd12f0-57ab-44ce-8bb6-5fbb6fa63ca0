package app_system

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	api "marketing/internal/api/system"
	"marketing/internal/model"
)

type AppSystem interface {
	GetAppSystemByKey(c *gin.Context, key string) (*model.AppSystemV2, error)
	GetAppSystemByID(c *gin.Context, id uint) (*model.AppSystemV2, error)
	Add(c *gin.Context, appSystem *model.AppSystemV2) error
	Update(c *gin.Context, appSystem *model.AppSystemV2) error
	Delete(c *gin.Context, id uint) error
	GetAppSystemList(c *gin.Context, req api.AppSystemReq) ([]model.AppSystemV2, int64, error)
	// GetUserGroupMap 不应该放这里，但是随便了
	GetUserGroupMap(c *gin.Context) (map[int]string, error)
}

type appSystem struct {
	db *gorm.DB
}

func NewAppSystem(db *gorm.DB) AppSystem {
	return &appSystem{
		db: db,
	}
}

func (s *appSystem) GetAppSystemByKey(c *gin.Context, key string) (*model.AppSystemV2, error) {
	var appSystem model.AppSystemV2
	err := s.db.WithContext(c).Where("app_key = ?", key).First(&appSystem).Error
	if err != nil {
		return nil, err
	}
	return &appSystem, nil
}
func (s *appSystem) GetAppSystemByID(c *gin.Context, id uint) (*model.AppSystemV2, error) {
	var appSystem model.AppSystemV2
	err := s.db.WithContext(c).Where("id =?", id).First(&appSystem).Error
	if err != nil {
		return nil, err
	}
	return &appSystem, nil
}

// Add 新增
func (s *appSystem) Add(c *gin.Context, appSystem *model.AppSystemV2) error {
	err := s.db.WithContext(c).Create(appSystem).Error
	if err != nil {
		return err
	}
	return nil
}

// Update 更新
func (s *appSystem) Update(c *gin.Context, appSystem *model.AppSystemV2) error {
	err := s.db.WithContext(c).Save(appSystem).Error
	if err != nil {
		return err
	}
	return nil
}

// Delete 删除
func (s *appSystem) Delete(c *gin.Context, id uint) error {
	err := s.db.WithContext(c).Delete(&model.AppSystemV2{}, id).Error
	if err != nil {
		return err
	}
	return nil
}

// GetAppSystemList 获取列表
func (s *appSystem) GetAppSystemList(c *gin.Context, req api.AppSystemReq) ([]model.AppSystemV2, int64, error) {
	var appSystems []model.AppSystemV2
	var total int64
	query := s.db.WithContext(c).Model(&model.AppSystemV2{})
	if req.AppKey != "" {
		query = query.Where("app_key = ?", req.AppKey)
	}
	if req.CorpID != "" {
		query = query.Where("corp_id =?", req.CorpID)
	}
	if req.Name != "" {
		query = query.Where("name like ?", "%"+req.Name+"%")
	}
	if req.Type != "" {
		query = query.Where("type =?", req.Type)
	}
	if req.UserGroup != 0 {
		query = query.Where("FIND_IN_SET(?, user_group) > 0", req.UserGroup)
	}
	if req.IsChild != nil {
		if *req.IsChild == 1 {
			query = query.Where("parent IS NOT NULL OR parent != ''")
		} else {
			query = query.Where("parent IS NULL OR parent = ''")
		}
	}
	if len(req.Parents) > 0 {
		query = query.Where("parent IN (?)", req.Parents)
	}
	if req.Parent != "" {
		query = query.Where("parent = ?", req.Parent)
	}

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	err = query.Limit(req.PageSize).Offset((req.Page - 1) * req.PageSize).Order("rank").Find(&appSystems).Error
	if err != nil {
		return nil, 0, err
	}
	return appSystems, total, nil
}

func (s *appSystem) GetUserGroupMap(c *gin.Context) (map[int]string, error) {
	var userGroups []model.AdminUserGroupV2
	err := s.db.WithContext(c).Find(&userGroups).Error
	if err != nil {
		return nil, err
	}
	userGroupMap := make(map[int]string)
	for _, value := range userGroups {
		userGroupMap[int(value.ID)] = value.Name
	}
	return userGroupMap, nil
}
